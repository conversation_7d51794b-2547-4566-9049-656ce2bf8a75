name: OpenMRS CI

on:
  workflow_dispatch:
  push:
    branches: [main]
  pull_request:
    branches: [main]
  release:
    types:
      - created

env:
  TURBO_API: 'http://127.0.0.1:9080'
  TURBO_TOKEN: 'turbo-token'
  TURBO_TEAM: ${{ github.repository_owner }}

jobs:
  build:
    runs-on: ubuntu-latest
    timeout-minutes: 15
    permissions:
      actions: read

    steps:
      - uses: actions/checkout@v4
      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"

      - name: 💾 Cache dependencies
        id: cache
        uses: actions/cache@v4
        with:
          path: '**/node_modules'
          key: ${{ runner.os }}-${{ hashFiles('**/yarn.lock') }}

      - name: 📦 Install dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: yarn install --immutable

      - name: 🚀 Setup local cache server for Turborepo
        uses: felixmosh/turborepo-gh-artifacts@v3
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          server-token: ${{ env.TURBO_TOKEN }}

      - name: 🧪 Run lint, type checks and tests
        run: yarn verify

      - name: 🏗️ Run build
        run: yarn turbo run build --concurrency=5

      - name: 📤 Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: packages
          path: |
            packages/**/dist
          overwrite: true

  pre_release:
    runs-on: ubuntu-latest
    needs: build
    if: ${{ github.event_name == 'push' && github.repository_owner == 'openmrs' }}

    steps:
      - uses: actions/checkout@v4
      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          registry-url: "https://registry.npmjs.org"

      - name: 💾 Cache dependencies
        id: cache
        uses: actions/cache@v4
        with:
          path: '**/node_modules'
          key: ${{ runner.os }}-${{ hashFiles('**/yarn.lock') }}

      - name: 📦 Install dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: yarn install --immutable

      - name: 🚀 Setup a local cache server for Turborepo
        uses: felixmosh/turborepo-gh-artifacts@v3
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          server-token: ${{ env.TURBO_TOKEN }}

      - name: 🏷️ Version
        run: yarn workspaces foreach --worktree --topological --exclude @openmrs/esm-patient-management version "$(node -e "console.log(require('semver').inc(require('./package.json').version, 'patch'))")-pre.${{ github.run_number }}"

      - name: 🏗️ Build
        run: yarn turbo run build --concurrency=5

      - name: 🔧 Configure Git
        run: git config user.email "<EMAIL>" && git config user.name "OpenMRS CI"
      - name: 💾 Commit changes
        run: git add . && git commit -m "Prerelease version" --no-verify

      - name: 🚀 Pre-release
        run: yarn config set npmAuthToken "${NODE_AUTH_TOKEN}" && yarn run ci:prepublish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}

      - name: 📤 Upload Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: packages
          path: |
            packages/**/dist
          overwrite: true

  release:
    runs-on: ubuntu-latest
    needs: build
    if: ${{ github.event_name == 'release' && github.repository_owner == 'openmrs' }}

    steps:
      - uses: actions/checkout@v4
      - name: 🟢 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          registry-url: "https://registry.npmjs.org"

      - name: 💾 Cache dependencies
        id: cache
        uses: actions/cache@v4
        with:
          path: '**/node_modules'
          key: ${{ runner.os }}-${{ hashFiles('**/yarn.lock') }}

      - name: 📦 Install dependencies
        if: steps.cache.outputs.cache-hit != 'true'
        run: yarn install --immutable

      - run: yarn turbo run build --color
      - run: yarn config set npmAuthToken "${NODE_AUTH_TOKEN}" && yarn run ci:publish
        env:
          NODE_AUTH_TOKEN: ${{ secrets.NPM_AUTH_TOKEN }}

  deploy_patient_management:
    runs-on: ubuntu-latest
    needs: pre_release
    if: ${{ github.event_name == 'push' }} && github.repository_owner == 'openmrs'

    steps:
      - name: 🔄 Trigger RefApp Build
        uses: fjogeleit/http-request-action@v1
        continue-on-error: true
        with:
          url: https://ci.openmrs.org/rest/api/latest/queue/O3-BF
          method: "POST"
          customHeaders: '{ "Authorization": "Bearer ${{ secrets.BAMBOO_TOKEN }}" }'
