on:
  workflow_dispatch:
  push:
    branches: [ main ]

name: "Update Transifex on Push"

jobs:
  push-translations-to-transifex:
    name: push-translations-to-transifex
    runs-on: ubuntu-latest
    permissions:
      actions: read
    if: github.repository_owner == 'openmrs'

    steps:
      - uses: actions/checkout@v4
      - name: Push source file using transifex client
        uses: transifex/cli-action@v2
        with:
          token: ${{ secrets.TRANSIFEX_TOKEN }}
