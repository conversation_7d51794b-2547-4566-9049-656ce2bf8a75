# This docker compose file is used to create a dockerized environment when running E2E tests on Bamboo.
version: "3.7"

services:
  playwright:
    build:
      context: .
      dockerfile: playwright.Dockerfile
      args:
        USER_ID: ${USER_ID}
        GROUP_ID: ${GROUP_ID}
    container_name: playwright-tests-container
    working_dir: /app
    command: sh /app/e2e/support/bamboo/e2e-test-runner.sh
    volumes:
      - ../../../:/app
    networks:
      - test

  gateway:
    image: openmrs/openmrs-reference-application-3-gateway:${TAG:-nightly}
    depends_on:
      - frontend
      - backend
    ports:
      - "80:80"
    networks:
      - test

  frontend:
    image: openmrs/openmrs-reference-application-3-frontend:${TAG:-nightly}
    environment:
      SPA_PATH: /openmrs/spa
      API_URL: /openmrs
      SPA_CONFIG_URLS:
    healthcheck:
      test: [ "CMD", "curl", "-f", "http://localhost/" ]
      timeout: 5s
    depends_on:
      - backend
    networks:
      - test

  backend:
    image: openmrs/openmrs-reference-application-3-backend:nightly-with-data
    depends_on:
      - db
    networks:
      - test
  db:
    image: openmrs/openmrs-reference-application-3-db:nightly-with-data
    networks:
      - test

networks:
  test:
