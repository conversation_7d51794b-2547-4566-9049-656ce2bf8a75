@use '@carbon/colors';
@use '@carbon/layout';
@use '@carbon/type';
@use '@openmrs/esm-styleguide/src/vars' as *;

.monthly-cell {
  border-left: 1px solid colors.$gray-20;
  border-bottom: 1px solid colors.$gray-20;
  background: colors.$white;
  color: colors.$black;
  cursor: pointer;
  padding: layout.$spacing-01;
  text-align: right;
  @include type.type-style('body-compact-02');

  &:nth-child(7n) {
    border-right: 1px solid colors.$gray-20;
  }

  &-disabled {
    border-left: 1px solid colors.$gray-20;
    border-bottom: 1px solid colors.$gray-20;
  }

  &-disabled:last-child {
    border-right: 1px solid colors.$gray-20;
  }
}

.identifiers {
  @include type.type-style('body-compact-02');
  color: $ui-04;
  display: list-item;

  span:not(:first-child) {
    margin: 0 layout.$spacing-04;
  }
}

.identifierTag {
  display: flex;
  align-items: center;
}

.weekly-cell {
  position: relative;
  border-right: 1px solid colors.$gray-20;
  border-bottom: 1px solid colors.$gray-20;
  min-height: 7.5rem;
  color: colors.$white;

  .week-time {
    display: none;
  }

  &:nth-child(-n + 8) {
    border-top: 1px solid colors.$gray-20;
  }

  &:first-child {
    border-top: none;
  }

  &:nth-child(8n + 1) {
    border-bottom: none;

    .week-time {
      white-space: nowrap;
      display: inline;
      color: colors.$white;
      position: absolute;
      top: -11px;
      height: layout.$spacing-06;
      width: layout.$spacing-07;
    }
  }
}

.currentData {
  display: flex;
  flex-direction: column;
  align-items: start;
  margin: 0.1rem 0.1rem layout.$spacing-03 0.1rem;

  & > span {
    @include type.type-style('label-02');
  }
}

.serviceArea {
  width: 100%;
  padding: layout.$spacing-01;
  margin-right: layout.$spacing-03;
  display: grid;
  grid-template-columns: 4fr 1fr;
  justify-content: flex-start;
  color: #020f1b;
  cursor: pointer;
  @include type.type-style('label-01');

  & > span:first-child {
    text-align: left;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }

  & > span:nth-child(2) {
    text-align: right;
  }
}

.serviceArea:hover {
  background-color: colors.$gray-10;
}

.totals {
  padding: layout.$spacing-01;
  display: grid;
  grid-template-columns: 4fr 1fr;
  justify-content: flex-start;
  background-color: colors.$gray-10;

  & > div {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    cursor: pointer;
    font-weight: lighter;
  }

  & > span:nth-child(2) {
    color: #020f1b;
    font-weight: bold;
    text-align: right;
    @include type.type-style('heading-compact-01');
  }
}

.smallDesktop {
  height: 6.25rem;
}

.largeDesktop {
  height: 9.375rem;
}

.red {
  background-color: colors.$red-70;
  color: colors.$white;
  font-weight: bold;
}

.purple {
  background-color: colors.$yellow-70;
  color: colors.$white;
  font-weight: bold;
}

.blue {
  background-color: colors.$blue-70;
  color: colors.$white;
  font-weight: bold;
}

.green {
  background-color: colors.$green-70;
  color: colors.$white;
  font-weight: bold;
}

.showMoreItems {
  margin-top: 0.1rem;
  padding: 0;
  @include type.type-style('heading-compact-01');
  text-decoration: underline;
  color: colors.$blue-50;
  background-color: colors.$white;
  font-weight: lighter;
  border: none;
  outline: none;
  cursor: pointer;
}
