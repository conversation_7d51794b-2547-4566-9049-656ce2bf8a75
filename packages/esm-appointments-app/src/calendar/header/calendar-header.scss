@use '@carbon/colors';
@use '@carbon/layout';
@use '@carbon/type';

.titleContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: layout.$spacing-05;

  & > p {
    @include type.type-style('heading-02');
  }

  & > button {
    color: colors.$blue-60;
    @include type.type-style('label-02');
  }
}

.titleContent {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.backButton {
  svg {
    order: 1;
    margin-right: layout.$spacing-03;
    margin-left: 0 !important;
  }

  span {
    order: 2;
  }
}

// Overriding styles for RTL support
html[dir='rtl'] {
  .titleContent {
    margin-left: layout.$spacing-05;
  }
}
