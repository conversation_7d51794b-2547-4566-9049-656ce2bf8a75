@use '@carbon/layout';
@use '@carbon/type';
@use '@carbon/colors';

.appointmentDetailsContainer {
  min-height: fit-content;
  background-color: colors.$white;
  padding: layout.$spacing-05;
  margin: layout.$spacing-03;
}

.title {
  @include type.type-style('heading-compact-01');
  color: colors.$gray-90;
}

.subTitle {
  @include type.type-style('label-01');
  color: colors.$gray-70;
}

.tags {
  margin-top: layout.$spacing-05;
  display: flex;
  column-gap: layout.$spacing-03;

  & > div {
    margin: 0;
  }
}

.patientInfoGrid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  column-gap: layout.$spacing-05;
  margin-top: layout.$spacing-05;
}

.gridTitle {
  @include type.type-style('label-02');
  margin-bottom: layout.$spacing-04;
  color: colors.$gray-70;
}

.label {
  @include type.type-style('label-01');
  color: colors.$gray-70;
}

.historyGrid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  row-gap: layout.$spacing-05;
}

.historyGridLabel {
  @include type.type-style('legal-01');
  color: colors.$gray-70;
}
.historyGridCount {
  @include type.type-style('heading-02');
  margin-top: layout.$spacing-01;
  color: colors.$blue-60;
}

.historyGridCountRed {
  @include type.type-style('heading-02');
  margin-top: layout.$spacing-01;
  color: colors.$red-60;
}

.labelBold {
  @include type.type-style('label-01');
  color: colors.$gray-70;
  font-weight: bold;
  margin-right: layout.$spacing-02;
}

.labelContainer {
  display: flex;
}
