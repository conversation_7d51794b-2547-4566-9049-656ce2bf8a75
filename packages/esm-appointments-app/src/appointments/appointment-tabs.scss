@use '@carbon/colors';
@use '@carbon/layout';
@use '@openmrs/esm-styleguide/src/vars' as *;

.appointmentList {
  margin: layout.$spacing-05;

  & > div {
    background-color: $ui-02;
  }
}

.tabs {
  grid-column: span 2;
}

.tab {
  min-width: 12rem;

  &:active,
  &:focus {
    outline: 2px solid var(--brand-03) !important;
  }

  &[aria-selected='true'] {
    box-shadow: inset 0 2px 0 0 var(--brand-03) !important;
  }
}

.tabPanel {
  padding: 0;
  margin: layout.$spacing-05;
}

.calendarButton {
  float: right;
  position: absolute;
  right: 0;
  height: layout.$spacing-09;
  min-height: 0;
}

.downloadButton {
  margin: layout.$spacing-05;
  & > button {
    border: 1px solid colors.$blue-60;
  }
}
.downloadLink {
  text-decoration: none;
  color: inherit;
}
