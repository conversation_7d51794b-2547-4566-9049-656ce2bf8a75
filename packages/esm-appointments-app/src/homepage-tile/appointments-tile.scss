@use '@openmrs/esm-styleguide/src/vars' as *;
@use '@carbon/type';
@use '@carbon/colors';
@use '@carbon/layout';

.tileContainer {
  display: flex;
  flex-direction: column;
  align-items: space-between;
  background-color: colors.$white;
  border: 1px solid colors.$gray-20;
  height: 7.875rem;
  padding: layout.$spacing-05;
  margin: layout.$spacing-03;
}

.tileHeader {
  @include type.type-style('heading-01');
  font-weight: 600;
  line-height: 1.28572;
  letter-spacing: 0.16px;
  color: colors.$gray-70;
}

.displayDetails {
  margin-top: layout.$spacing-06;
}

.displayData {
  font-size: 1.75rem;
  font-weight: 400;
  line-height: 1.28572;
  color: colors.$black;
}

.countLabel {
  @include type.type-style('label-01');
  color: $text-02;
}
