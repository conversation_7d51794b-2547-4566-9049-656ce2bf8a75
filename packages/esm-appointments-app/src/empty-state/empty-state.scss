@use '@carbon/layout';
@use '@carbon/type';
@use '@openmrs/esm-styleguide/src/vars' as *;

.content {
  @include type.type-style('heading-compact-01');
  color: $text-02;
  margin-top: layout.$spacing-05;
  margin-bottom: layout.$spacing-03;
}

.displayText {
  text-transform: lowercase;
}

.desktopHeading {
  h4 {
    @include type.type-style('heading-compact-02');
    color: $text-02;
  }
}

.tabletHeading {
  h4 {
    @include type.type-style('heading-03');
    color: $text-02;
  }
}

.desktopHeading,
.tabletHeading {
  text-align: left;
  text-transform: capitalize;
  margin-bottom: layout.$spacing-05;

  h4:after {
    content: '';
    display: block;
    width: layout.$spacing-07;
    padding-top: 0.188rem;
    border-bottom: 0.375rem solid var(--brand-03);
  }
}

.heading:after {
  content: '';
  display: block;
  width: layout.$spacing-07;
  padding-top: 0.188rem;
  border-bottom: 0.375rem solid var(--brand-03);
}

.tile {
  text-align: center;
  border: 1px solid $ui-03;
}

.header4 {
  @include type.type-style('heading-compact-01');
  margin-bottom: layout.$spacing-05;
}

// Overriding styles for RTL support
html[dir='rtl'] {
  .desktopHeading,
  .tabletHeading {
    text-align: right;
  }
}
