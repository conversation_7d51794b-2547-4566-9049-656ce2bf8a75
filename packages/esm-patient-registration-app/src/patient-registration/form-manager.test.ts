import { FormManager } from './form-manager';
import { type FormValues } from './patient-registration.types';
import { generateIdentifier } from './patient-registration.resource';

jest.mock('./patient-registration.resource', () => ({
  ...jest.requireActual('./patient-registration.resource'),
  generateIdentifier: jest.fn(),
}));

const mockGenerateIdentifier = generateIdentifier as jest.Mock;

const formValues: FormValues = {
  patientUuid: 'test-patient-uuid',
  givenName: 'John',
  middleName: 'Doe',
  familyName: '<PERSON>',
  additionalGivenName: 'Johnny',
  additionalMiddleName: 'D',
  additionalFamilyName: 'S',
  addNameInLocalLanguage: false,
  gender: 'M',
  birthdate: new Date('1990-01-01'),
  birthdateEstimated: false,
  yearsEstimated: 0,
  monthsEstimated: 0,
  address: {},
  attributes: {},
  identifiers: {},
  relationships: [],
  telephoneNumber: '**********',
  isDead: false,
  deathDate: undefined,
  deathTime: undefined,
  deathTimeFormat: 'AM',
  deathCause: '',
  nonCodedCauseOfDeath: '',
  obs: {},
};

describe('FormManager', () => {
  describe('createIdentifiers', () => {
    it('uses the uuid of a field name if it exists', async () => {
      const result = await FormManager.savePatientIdentifiers(true, undefined, formValues.identifiers, {}, 'Nyc');
      expect(result).toEqual([
        {
          uuid: 'aUuid',
          identifier: 'foo',
          identifierType: 'identifierType',
          location: 'Nyc',
          preferred: true,
        },
      ]);
    });

    it('should generate identifier if it has autoGeneration and manual entry disabled', async () => {
      formValues.identifiers.foo.autoGeneration = true;
      formValues.identifiers.foo.selectedSource.autoGenerationOption.manualEntryEnabled = false;
      mockGenerateIdentifier.mockResolvedValue({ data: { identifier: '10001V' } });
      await FormManager.savePatientIdentifiers(true, undefined, formValues.identifiers, {}, 'Nyc');
      expect(mockGenerateIdentifier.mock.calls).toHaveLength(1);
    });

    it('should not generate identifiers if manual entry enabled and identifier value given', async () => {
      formValues.identifiers.foo.autoGeneration = true;
      formValues.identifiers.foo.selectedSource.autoGenerationOption.manualEntryEnabled = true;
      await FormManager.savePatientIdentifiers(true, undefined, formValues.identifiers, {}, 'Nyc');
      expect(mockGenerateIdentifier.mock.calls).toHaveLength(0);
    });
  });
});
