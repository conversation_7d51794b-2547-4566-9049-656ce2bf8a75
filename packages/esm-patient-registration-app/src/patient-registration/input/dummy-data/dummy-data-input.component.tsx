import React from 'react';
import classNames from 'classnames';
import { v4 } from 'uuid';
import { type FormValues } from '../../patient-registration.types';
import styles from './../input.scss';

interface DummyDataInputProps {
  setValues(values: FormValues, shouldValidate?: boolean): void;
}

export const dummyFormValues: FormValues = {
  patientUuid: 'dummy-patient-uuid',
  givenName: 'Dummy',
  middleName: 'M',
  familyName: 'Patient',
  additionalGivenName: 'Dummy',
  additionalMiddleName: 'M',
  additionalFamilyName: 'P',
  addNameInLocalLanguage: true,
  gender: 'F',
  birthdate: new Date('1985-06-15'),
  birthdateEstimated: false,
  yearsEstimated: 0,
  monthsEstimated: 0,
  address: {
    address1: '123 Dummy Street',
    address2: 'Apt 4B',
    cityVillage: 'Dummy City',
    stateProvince: 'Dummy State',
    country: 'Dummy Country',
    postalCode: '12345',
  },
  attributes: {},
  identifiers: {},
  relationships: [],
  telephoneNumber: '**********',
  isDead: false,
  deathDate: undefined,
  deathTime: undefined,
  deathTimeFormat: 'AM',
  deathCause: '',
  nonCodedCauseOfDeath: '',
  obs: {},
};

export const DummyDataInput: React.FC<DummyDataInputProps> = ({ setValues }) => {
  return (
    <main>
      <button
        className={classNames('omrs-btn omrs-filled-neutral', styles.dummyData)}
        onClick={() => setValues(dummyFormValues)}
        type="button"
        aria-label="Dummy Data Input">
        Input Dummy Data
      </button>
    </main>
  );
};
