import React, { useMemo, useState, useCallback } from 'react';
import {
  Modal,
  Button,
  TextInput,
  Select,
  SelectItem,
  DatePicker,
  DatePickerInput,
  NumberInput,
  RadioButton,
  RadioButtonGroup,
  Form,
  FormGroup,
} from '@carbon/react';
import dayjs from 'dayjs';
import { useTranslation } from 'react-i18next';
import { savePatient, generateIdentifier } from '../../patient-registration.resource';
import { useResourcesContext } from '../../../resources-context';
import { useConfig } from '@openmrs/esm-framework';
import { type RegistrationConfig } from '../../../config-schema';
import { initializeIdentifier } from '../../field/id/id-field.component';
import { type PatientIdentifierValue } from '../../patient-registration.types';

interface CreatePersonModalProps {
  onClose: () => void;
  onPersonCreated: (personUuid: string, personName: string) => void;
}

export const CreatePersonModal: React.FC<CreatePersonModalProps> = ({ onClose, onPersonCreated }) => {
  const { t } = useTranslation();
  const { identifierTypes } = useResourcesContext();
  const config = useConfig<RegistrationConfig>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [givenName, setGivenName] = useState('');
  const [familyName, setFamilyName] = useState('');
  const [gender, setGender] = useState('');
  const [useBirthdate, setUseBirthdate] = useState<'birthdate' | 'age'>('birthdate');
  const [birthdate, setBirthdate] = useState<string>('');
  const [ageYears, setAgeYears] = useState<string>('');
  const [ageMonths, setAgeMonths] = useState<string>('');
  const [identifiers, setIdentifiers] = useState<Record<string, PatientIdentifierValue>>({});

  // Get required identifier types and initialize them
  const requiredIdentifierTypes = useMemo(() => {
    if (!identifierTypes) return [];
    return identifierTypes.filter(
      (type) =>
        type.isPrimary ||
        type.required ||
        !!config.defaultPatientIdentifierTypes?.find(
          (defaultIdentifierTypeUuid) => defaultIdentifierTypeUuid === type.uuid,
        ),
    );
  }, [identifierTypes, config.defaultPatientIdentifierTypes]);

  // Initialize identifiers when types change
  React.useEffect(() => {
    if (requiredIdentifierTypes.length > 0) {
      const initialIdentifiers: Record<string, PatientIdentifierValue> = {};
      requiredIdentifierTypes.forEach((type) => {
        initialIdentifiers[type.fieldName] = initializeIdentifier(type, {});
      });
      setIdentifiers(initialIdentifiers);
    }
  }, [requiredIdentifierTypes]);

  const isCreateDisabled = useMemo(() => {
    if (!givenName || !familyName || !gender) return true;
    if (useBirthdate === 'birthdate') {
      return !birthdate;
    }
    // Using age - allow empty strings for better UX
    return ageYears === '' && ageMonths === '';
  }, [givenName, familyName, gender, useBirthdate, birthdate, ageYears, ageMonths]);

  // Check if all required identifiers are valid
  const areIdentifiersValid = useMemo(() => {
    if (requiredIdentifierTypes.length === 0) return true;
    return requiredIdentifierTypes.every((type) => {
      const identifier = identifiers[type.fieldName];
      if (!identifier) return false;

      // For auto-generated identifiers, just check if source is selected
      if (identifier.autoGeneration && identifier.selectedSource) {
        return true;
      }

      // For manual entry, check if value is provided
      return identifier.identifierValue && identifier.identifierValue.trim() !== '';
    });
  }, [requiredIdentifierTypes, identifiers]);

  const isFormValid = useMemo(() => {
    return !isCreateDisabled && areIdentifiersValid;
  }, [isCreateDisabled, areIdentifiersValid]);

  const handleSubmit = async () => {
    if (!isFormValid) return;
    setIsSubmitting(true);

    try {
      let computedBirthdate: string | undefined = undefined;
      let birthdateEstimated = false;

      if (useBirthdate === 'birthdate' && birthdate) {
        // birthdate comes as yyyy-mm-dd
        computedBirthdate = dayjs(birthdate).format('YYYY-MM-DD');
        birthdateEstimated = false;
      } else {
        // derive from age - only if both fields have values
        if (ageYears !== '' || ageMonths !== '') {
          const now = dayjs();
          const years = ageYears !== '' ? parseInt(ageYears, 10) : 0;
          const months = ageMonths !== '' ? parseInt(ageMonths, 10) : 0;
          const derived = now.subtract(years, 'year').subtract(months, 'month');
          computedBirthdate = derived.format('YYYY-MM-DD');
          birthdateEstimated = true;
        }
      }

      // Build identifiers array with auto-generation support
      const identifiersArray = await Promise.all(
        requiredIdentifierTypes.map(async (type) => {
          const identifier = identifiers[type.fieldName];
          let identifierValue = identifier.identifierValue;

          // Generate identifier if auto-generation is enabled
          if (identifier.autoGeneration && identifier.selectedSource) {
            try {
              const response = await generateIdentifier(identifier.selectedSource.uuid);
              identifierValue = response.data.identifier;
            } catch (error) {
              console.error('Failed to generate identifier:', error);
              throw new Error(`Failed to generate ${type.name}`);
            }
          }

          return {
            identifier: identifierValue,
            identifierType: type.uuid,
            preferred: type.isPrimary,
          };
        }),
      );

      // Ensure at least one identifier is marked as preferred
      if (identifiersArray.length > 0 && !identifiersArray.some((id) => id.preferred)) {
        identifiersArray[0].preferred = true;
      }

      const patientPayload: any = {
        identifiers: identifiersArray,
        person: {
          names: [
            {
              preferred: true,
              givenName,
              middleName: '',
              familyName,
            },
          ],
          gender,
          birthdate: computedBirthdate,
          birthdateEstimated,
          attributes: [],
          addresses: [],
          dead: false,
        },
      };

      const response = await savePatient(patientPayload as any);
      const createdUuid = response?.data?.uuid;
      if (createdUuid) {
        // Create full name for the relationship field
        const fullName = `${givenName} ${familyName}`.trim();
        onPersonCreated(createdUuid, fullName);
      }
      onClose();
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error creating person', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleIdentifierChange = (fieldName: string, value: string) => {
    setIdentifiers((prev) => ({
      ...prev,
      [fieldName]: {
        ...prev[fieldName],
        identifierValue: value,
      },
    }));
  };

  return (
    <Modal
      open
      modalHeading={t('createPersonModalTitle', 'Create new person')}
      primaryButtonText={t('create', 'Create')}
      secondaryButtonText={t('cancel', 'Cancel')}
      onRequestClose={onClose}
      onRequestSubmit={handleSubmit}
      primaryButtonDisabled={isSubmitting || !isFormValid}>
      <Form>
        <FormGroup legendText={t('personDetailsLegend', 'Person details')}>
          <TextInput
            id="givenName"
            labelText={t('givenNameLabel', 'Given name')}
            value={givenName}
            onChange={(e) => setGivenName(e.target.value)}
            required
          />
          <TextInput
            id="familyName"
            labelText={t('familyNameLabel', 'Family name')}
            value={familyName}
            onChange={(e) => setFamilyName(e.target.value)}
            required
          />
          <Select
            id="gender"
            labelText={t('genderLabel', 'Gender')}
            value={gender}
            onChange={(e) => setGender((e.target as HTMLSelectElement).value)}
            required>
            <SelectItem value="" text={t('selectGenderPlaceholder', 'Select gender')} />
            <SelectItem value="M" text={t('maleLabel', 'Male')} />
            <SelectItem value="F" text={t('femaleLabel', 'Female')} />
            <SelectItem value="O" text={t('otherLabel', 'Other')} />
            <SelectItem value="U" text={t('unknownLabel', 'Unknown')} />
          </Select>

          <RadioButtonGroup
            name="birthdateOrAge"
            legendText={t('dateOfBirthEntryMode', 'Provide date of birth as')}
            valueSelected={useBirthdate}
            onChange={(val: 'birthdate' | 'age') => setUseBirthdate(val)}
            orientation="horizontal">
            <RadioButton id="useBirthdate" labelText={t('birthdate', 'Birthdate')} value="birthdate" />
            <RadioButton id="useAge" labelText={t('age', 'Age')} value="age" />
          </RadioButtonGroup>

          {useBirthdate === 'birthdate' ? (
            <DatePicker
              dateFormat="Y-m-d"
              datePickerType="single"
              onChange={([date]) => {
                if (date) setBirthdate(dayjs(date as unknown as Date).format('YYYY-MM-DD'));
              }}>
              <DatePickerInput
                id="birthdate"
                labelText={t('birthdateLabel', 'Date of birth')}
                placeholder="YYYY-MM-DD"
              />
            </DatePicker>
          ) : (
            <div style={{ display: 'flex', gap: '1rem' }}>
              <TextInput
                id="ageYears"
                type="number"
                labelText={t('ageYears', 'Age (years)')}
                value={ageYears}
                onChange={(e) => setAgeYears(e.target.value)}
                min={0}
                max={150}
                placeholder="0"
              />
              <TextInput
                id="ageMonths"
                type="number"
                labelText={t('ageMonths', 'Age (months)')}
                value={ageMonths}
                onChange={(e) => setAgeMonths(e.target.value)}
                min={0}
                max={11}
                placeholder="0"
              />
            </div>
          )}

          {/* Identifier Fields */}
          {requiredIdentifierTypes.length > 0 && (
            <FormGroup legendText={t('identifiersLegend', 'Identifiers')}>
              {requiredIdentifierTypes.map((type) => {
                const identifier = identifiers[type.fieldName];
                if (!identifier) return null;

                const isAutoGenerated = identifier.autoGeneration && identifier.selectedSource;
                const showInput =
                  !isAutoGenerated || identifier.selectedSource?.autoGenerationOption?.manualEntryEnabled;

                return (
                  <div key={type.uuid} style={{ marginBottom: '1rem' }}>
                    {showInput ? (
                      <TextInput
                        id={`identifier-${type.fieldName}`}
                        labelText={type.name}
                        value={identifier.identifierValue || ''}
                        onChange={(e) => handleIdentifierChange(type.fieldName, e.target.value)}
                        required={type.required || type.isPrimary}
                        helperText={type.formatDescription || type.format}
                      />
                    ) : (
                      <div style={{ padding: '0.75rem', backgroundColor: '#f4f4f4', borderRadius: '4px' }}>
                        <p style={{ margin: '0', fontWeight: '600' }}>{type.name}</p>
                        <p style={{ margin: '0.25rem 0 0 0', color: '#666' }}>
                          {t('autoGeneratedPlaceholderText', 'Auto-generated')}
                        </p>
                      </div>
                    )}
                  </div>
                );
              })}
            </FormGroup>
          )}
        </FormGroup>
      </Form>

      <div style={{ display: 'none' }}>
        {/* Force Modal primary button to trigger handleSubmit */}
        <Button kind="primary" onClick={handleSubmit} disabled={isSubmitting || !isFormValid}>
          {t('create', 'Create')}
        </Button>
      </div>
    </Modal>
  );
};

export default CreatePersonModal;
