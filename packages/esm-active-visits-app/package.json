{"name": "@openmrs/esm-active-visits-app", "version": "9.0.0", "description": "Active visits widget microfrontend for O3", "browser": "dist/openmrs-esm-active-visits-app.js", "main": "src/index.ts", "source": true, "license": "MPL-2.0", "homepage": "https://github.com/openmrs/openmrs-esm-patient-management#readme", "scripts": {"start": "openmrs develop", "serve": "webpack serve --mode=development", "debug": "npm run serve", "build": "webpack --mode production", "analyze": "webpack --mode=production --env.analyze=true", "lint": "cross-env eslint src --ext ts,tsx", "test": "cross-env TZ=UTC jest --config jest.config.js --verbose false --passWithNoTests --color", "test:watch": "cross-env TZ=UTC jest --watch --config jest.config.js --color", "coverage": "yarn test --coverage", "typescript": "tsc", "extract-translations": "i18next 'src/**/*.component.tsx' 'src/**/*.resource.tsx' 'src/**/*.extension.tsx' 'src/**/*modal.tsx' 'src/**/*.workspace.tsx' 'src/index.ts' --config ../../tools/i18next-parser.config.js"}, "browserslist": ["extends browserslist-config-openmrs"], "keywords": ["openmrs"], "publishConfig": {"access": "public"}, "repository": {"type": "git", "url": "git+https://github.com/openmrs/openmrs-esm-patient-management.git"}, "bugs": {"url": "https://github.com/openmrs/openmrs-esm-patient-management/issues"}, "dependencies": {"@carbon/react": "^1.83.0", "classnames": "^2.3.2", "lodash-es": "^4.17.15"}, "peerDependencies": {"@openmrs/esm-framework": "6.x", "dayjs": "1.x", "react": "^18.1.0", "react-dom": "^18.1.0", "react-i18next": "11.x", "swr": "2.x"}, "devDependencies": {"webpack": "^5.99.9"}}