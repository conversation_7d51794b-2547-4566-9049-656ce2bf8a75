@use '@carbon/layout';
@use '@carbon/type';
@use '@openmrs/esm-styleguide/src/vars' as *;

.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
}

.encounterHeading {
  text-align: left;
  width: 100%;
  margin: 0 layout.$spacing-05 1.3125rem;
  color: $ui-05;
}

.medicationRecord {
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .bodyLong01 {
    margin: layout.$spacing-02 0;
  }
}

.medicationContainer {
  background-color: $ui-01;
  padding: layout.$spacing-05;
  width: 100% !important;
}

.dosage {
  @include type.type-style('heading-compact-01');
}

.metadata {
  @include type.type-style('label-01');
  color: $text-02;
  margin: layout.$spacing-03 0 layout.$spacing-05;
}

.visitsDetailWidgetContainer {
  background-color: $ui-background;
  width: 100%;
  border: 1px solid $ui-03;
}

.visitsDetailHeaderContainer {
  display: flex;
  justify-content: space-between;
  padding: layout.$spacing-04 0 layout.$spacing-04 layout.$spacing-05;
  background-color: $ui-background;
}

.visitsDetailHeaderContainer > h4:after {
  content: '';
  display: block;
  width: layout.$spacing-07;
  padding-top: 0.188rem;
  border-bottom: 0.375rem solid $brand-teal-01;
}

.customTable {
  th {
    padding: 0 !important;
  }

  tr[data-parent-row]:nth-child(odd) td {
    background-color: $ui-02;
  }

  tbody tr[data-parent-row]:nth-child(even) td {
    background-color: $ui-01;
  }

  td {
    border-bottom: none !important;
  }
}

.visitEmptyState {
  text-align: center;
  background-color: white;
  padding: layout.$spacing-07;
  border: 1px solid $ui-03;
  width: 100%;
}

.encounterEmptyState {
  text-align: center;
  margin: 0 layout.$spacing-05 layout.$spacing-05 layout.$spacing-05;
}

.expandedRow > td {
  padding: inherit !important;
}

.expandedRow > td > div {
  max-height: max-content !important;
}

.observation {
  display: grid;
  grid-template-columns: max-content auto;
  grid-gap: layout.$spacing-03;
  margin: layout.$spacing-03 0;
}

.observation > span {
  align-self: center;
}

.summaryContainer {
  background-color: $ui-background;
  display: grid;
  grid-template-columns: max-content auto;
  padding: layout.$spacing-05 0;
  margin: 0 layout.$spacing-05;

  :global(.cds--tabs) {
    max-height: 7rem;
  }
}

.flexSections {
  display: flex;
}

.verticalTabs {
  margin: layout.$spacing-05 0;
  scroll-behavior: smooth;

  > ul {
    flex-direction: column !important;
  }

  :global(.cds--tabs--scrollable .cds--tabs--scrollable__nav-item + .cds--tabs--scrollable__nav-item) {
    margin-left: 0;
  }

  :global(.cds--tabs--scrollable .cds--tabs--scrollable__nav-link) {
    border-bottom: 0 !important;
    border-left: 2px solid $color-gray-30;
  }
}

.tab {
  outline: 0;
  outline-offset: 0;
  min-height: layout.$spacing-07;

  &:active,
  &:focus {
    outline: 2px solid var(--brand-03) !important;
  }

  &[aria-selected='true'] {
    border-left: 3px solid var(--brand-03);
    border-bottom: none;
    font-weight: 600;
    margin-left: 0 !important;
  }

  &[aria-selected='false'] {
    border-bottom: none;
    border-left: 2px solid $ui-03;
    margin-left: 0 !important;
  }
}

.tablist {
  :global(.cds--tab--list) {
    flex-direction: column;
    max-height: fit-content;
    overflow-x: visible;
  }

  > button :global(.cds--tabs .cds--tabs__nav-link) {
    border-bottom: none;
  }
}

.medicationBlock {
  background-color: $ui-01;
  padding: 0.625rem 6.75rem layout.$spacing-04 1.063rem;
  margin-top: layout.$spacing-06;
  width: 100% !important;
}

.medicationBlock:first-child {
  margin-top: 0;
}

.diagnosisLabel {
  @include type.type-style('heading-compact-01');
  color: $text-02;
  margin-top: 5px;
}

.diagnosesList {
  display: flex;
  flex-flow: row wrap;
  padding-bottom: layout.$spacing-03;
  margin: 0 layout.$spacing-05;
  border-bottom: 1px solid $ui-03;
}

.actions {
  margin: 0 layout.$spacing-05;
}

.contentSwitcher {
  // TODO: Remove once override gets added to styleguide
  // Hey Dennis, did this happen?
  :global(.cds--content-switcher-btn) {
    min-width: fit-content;
  }

  :global(.cds--content-switcher__label) {
    height: layout.$spacing-05;
  }
}

.notesContainer {
  margin-bottom: layout.$spacing-07;
}

.noteText {
  background-color: $ui-01;
  padding: layout.$spacing-05;
  width: 100% !important;
  white-space: pre-wrap;
}

.desktopHeading,
.tabletHeading {
  text-align: left;
  text-transform: capitalize;
  margin-bottom: layout.$spacing-05;

  h4 {
    @include type.type-style('heading-compact-02');
    color: $text-02;

    &:after {
      content: '';
      display: block;
      width: layout.$spacing-07;
      padding-top: 3px;
      border-bottom: 0.375rem solid;
      @include brand-03(border-bottom-color);
    }
  }
}

.tile {
  text-align: center;
}

.emptyStateContent {
  @include type.type-style('heading-compact-01');
  color: $text-02;
  margin-top: layout.$spacing-05;
  margin-bottom: layout.$spacing-03;
}

.emptyStateContainer {
  background-color: $ui-02;
  border: 1px solid $ui-03;
  width: 100%;
  margin: 0 auto;
  max-width: 95vw;
  padding-bottom: 0;
}

.productiveHeading02 {
  @include type.type-style('heading-compact-02');
}

.bodyLong01 {
  @include type.type-style('body-01');
}

.caption01 {
  @include type.type-style('legal-01');
}

.bodyShort02 {
  @include type.type-style('body-compact-02');
}

.text02 {
  color: $text-02;
}

.text01 {
  color: $ui-05;
}

// Overriding styles for RTL support
html[dir='rtl'] {
  .visitsDetailHeaderContainer {
    padding: layout.$spacing-04 layout.$spacing-05 layout.$spacing-04 0;
    h4 {
      text-align: right;
    }
    & > div {
      & > div {
        & :first-child {
          border-bottom-left-radius: unset;
          border-top-left-radius: unset;
          border-bottom-right-radius: layout.$spacing-02;
          border-top-right-radius: layout.$spacing-02;
        }
        & :first-child[aria-selected='false'] {
          border-left: unset;
          border-right: 0.0625rem solid #a6c8ff;
        }
        & :last-child {
          border-bottom-right-radius: unset;
          border-top-right-radius: unset;
          border-bottom-left-radius: layout.$spacing-02;
          border-top-left-radius: layout.$spacing-02;
        }
        & :last-child[aria-selected='false'] {
          border-right: unset;
          border-left: 0.0625rem solid #a6c8ff;
        }
      }
    }
  }
  .summaryContainer {
    .tablist {
      & > div {
        button {
          text-align: right;
        }
        button[aria-selected='true'] {
          border-left: unset;
          border-right: 3px solid var(--brand-03);
        }
        button[aria-selected='false'] {
          border-left: unset;
          border-right: 2px solid #e0e0e0;
        }
      }
    }
  }
}
