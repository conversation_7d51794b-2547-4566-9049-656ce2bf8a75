@use '@carbon/layout';
@use '@carbon/type';
@use '@openmrs/esm-styleguide/src/vars' as *;

.container {
  margin: 0 layout.$spacing-05;
}

.activeVisitsContainer {
  background-color: $ui-02;
  border: 1px solid $ui-03;
  width: 100%;
  margin: 0 auto;
  max-width: 95vw;
  padding-bottom: 0;
}

.activeVisitsDetailHeaderContainer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: layout.$spacing-04 0 layout.$spacing-04 layout.$spacing-05;
  background-color: $ui-02;
}

.activeVisitsTable {
  width: 100%;

  th,
  td {
    white-space: nowrap;
    text-align: left;
  }

  tbody tr[data-parent-row] {
    // Don't show a bottom border on the last row so we don't end up with a double border from the activeVisitContainer
    &:nth-last-of-type(2) > td {
      border-bottom: none;
    }
  }
}

.backgroundDataFetchingIndicator {
  align-items: center;
  display: flex;
  flex: 1 1 0%;
  justify-content: center;
}

.tableContainer section {
  position: relative;
}

.tableContainer a {
  text-decoration: none;
}

.pagination {
  overflow: hidden;

  &:global(.cds--pagination) {
    border-top: 1px solid $ui-03;
  }
}

.hiddenRow {
  display: none;
}

.emptyRow {
  padding: 0 layout.$spacing-05;
  display: flex;
  align-items: center;
}

.visitSummaryContainer {
  width: 100%;
  max-width: 48rem;
  margin: layout.$spacing-05 auto;
}

.expandedActiveVisitRow {
  td {
    padding: 0 layout.$spacing-07;

    > div {
      max-height: max-content !important;
    }
  }

  th[colspan] td[colspan] > div:first-child {
    padding: 0 layout.$spacing-05;
  }

  &:last-of-type th[colspan]:last-child {
    border-bottom: none;
  }
}

.action {
  margin-bottom: layout.$spacing-03;
}

.content {
  @include type.type-style('heading-compact-01');
  color: $text-02;
  margin-top: layout.$spacing-05;
  margin-bottom: layout.$spacing-03;
}

.desktopHeading,
.tabletHeading {
  text-align: left;
  text-transform: capitalize;

  h4 {
    @include type.type-style('heading-compact-02');
    color: $text-02;

    &:after {
      content: '';
      display: block;
      width: layout.$spacing-07;
      padding-top: 3px;
      border-bottom: 0.375rem solid;
      @include brand-03(border-bottom-color);
    }
  }
}

.tile {
  text-align: center;
}

.filterEmptyState {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: layout.$spacing-05;
  margin: layout.$spacing-11;
  text-align: center;
}

.filterEmptyStateTile {
  margin: auto;
}

.filterEmptyStateContent {
  @include type.type-style('heading-compact-02');
  color: $text-02;
  margin-bottom: layout.$spacing-03;
}

.filterEmptyStateHelper {
  @include type.type-style('body-compact-01');
  color: $text-02;
}

// Overriding styles for RTL support
html[dir='rtl'] {
  .activeVisitsContainer {
    .activeVisitsDetailHeaderContainer {
      padding: layout.$spacing-04 layout.$spacing-05 layout.$spacing-04 0;
    }

    .desktopHeading,
    .tabletHeading {
      h4 {
        text-align: right;
      }
    }

    div[role='search'] {
      & :first-child {
        svg {
          left: unset;
          right: layout.$spacing-03;
        }
      }

      & :last-child {
        right: unset;
        left: 0;
      }
    }

    .tableContainer {
      overflow-x: auto;
      text-wrap: nowrap;

      th > div {
        text-align: right;
      }

      td {
        text-align: right;

        .serviceColor {
          margin-right: 0;
          margin-left: layout.$spacing-03;
        }

        button {
          text-align: right;
        }
      }
    }
  }
}
