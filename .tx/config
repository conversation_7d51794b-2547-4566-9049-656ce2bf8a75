[main]
host = https://www.transifex.com

[o:openmrs:p:openmrs3:r:esm-active-visits-app]
file_filter            = packages/esm-active-visits-app/translations/<lang>.json
source_file            = packages/esm-active-visits-app/translations/en.json
source_lang            = en
type                   = KEYVALUEJSON
replace_edited_strings = false
keep_translations      = false
resource_name          = esm-active-visits-app

[o:openmrs:p:openmrs3:r:esm-appointments-app]
file_filter            = packages/esm-appointments-app/translations/<lang>.json
source_file            = packages/esm-appointments-app/translations/en.json
source_lang            = en
type                   = KEYVALUEJSON
replace_edited_strings = false
keep_translations      = false
resource_name          = esm-appointments-app

[o:openmrs:p:openmrs3:r:esm-bed-management-app]
file_filter            = packages/esm-bed-management-app/translations/<lang>.json
source_file            = packages/esm-bed-management-app/translations/en.json
source_lang            = en
type                   = KEYVALUEJSON
replace_edited_strings = false
keep_translations      = false
resource_name          = esm-bed-management-app

[o:openmrs:p:openmrs3:r:esm-home-app]
file_filter            = packages/esm-home-app/translations/<lang>.json
source_file            = packages/esm-home-app/translations/en.json
source_lang            = en
type                   = KEYVALUEJSON
replace_edited_strings = false
keep_translations      = false
resource_name          = esm-home-app

[o:openmrs:p:openmrs3:r:esm-patient-list-management-app]
file_filter            = packages/esm-patient-list-management-app/translations/<lang>.json
source_file            = packages/esm-patient-list-management-app/translations/en.json
source_lang            = en
type                   = KEYVALUEJSON
replace_edited_strings = false
keep_translations      = false
resource_name          = esm-patient-list-management-app

[o:openmrs:p:openmrs3:r:esm-patient-registration-app]
file_filter            = packages/esm-patient-registration-app/translations/<lang>.json
source_file            = packages/esm-patient-registration-app/translations/en.json
source_lang            = en
type                   = KEYVALUEJSON
replace_edited_strings = false
keep_translations      = false
resource_name          = esm-patient-registration-app

[o:openmrs:p:openmrs3:r:esm-patient-search-app]
file_filter            = packages/esm-patient-search-app/translations/<lang>.json
source_file            = packages/esm-patient-search-app/translations/en.json
source_lang            = en
type                   = KEYVALUEJSON
replace_edited_strings = false
keep_translations      = false
resource_name          = esm-patient-search-app

[o:openmrs:p:openmrs3:r:esm-service-queues-app]
file_filter            = packages/esm-service-queues-app/translations/<lang>.json
source_file            = packages/esm-service-queues-app/translations/en.json
source_lang            = en
type                   = KEYVALUEJSON
replace_edited_strings = false
keep_translations      = false
resource_name          = esm-service-queues-app

[o:openmrs:p:openmrs3:r:esm-ward-app]
file_filter            = packages/esm-ward-app/translations/<lang>.json
source_file            = packages/esm-ward-app/translations/en.json
source_lang            = en
type                   = KEYVALUEJSON
replace_edited_strings = false
keep_translations      = false
resource_name          = esm-ward-app
