export const mockAppointmentsData = {
  data: [
    {
      uuid: '7cd38a6d-377e-491b-8284-b04cf8b8c6d8',
      appointmentNumber: '0000',
      patient: {
        identifier: '100GEJ',
        name: '<PERSON>',
        uuid: '8673ee4f-e2ab-4077-ba55-4980f408773e',
        gender: 'M',
        age: 35,
        birthdate: '1986-04-03T00:00:00.000+0000',
        phoneNumber: '**********',
      },
      service: {
        appointmentServiceId: 1,
        name: 'Outpatient',
        description: null,
        speciality: {},
        startTime: '',
        endTime: '',
        maxAppointmentsLimit: null,
        durationMins: null,
        location: {},
        uuid: 'e2ec9cf0-ec38-4d2b-af6c-59c82fa30b90',
        initialAppointmentStatus: 'Scheduled',
        creatorName: null,
      },
      serviceType: {
        display: 'HIV Clinic',
        uuid: '53d58ff1-0c45-4e2e-9bd2-9cc826cb46e1',
        duration: 15,
      },
      provider: {
        uuid: 'f9badd80-ab76-11e2-9e96-0800200c9a66',
        person: { uuid: '24252571-dd5a-11e6-9d9c-0242ac150002', display: 'Dr James Cook' },
      },
      location: { name: 'HIV Clinic', uuid: '2131aff8-2e2a-480a-b7ab-4ac53250262b' },
      startDateTime: 1630326900000,
      endDateTime: 1630327200000,
      appointmentKind: 'WalkIn',
      status: 'Scheduled',
      comments: 'Walk in appointments',
      additionalInfo: null,
      providers: [{ uuid: '24252571-dd5a-11e6-9d9c-0242ac150002', display: 'Dr James Cook' }],
      recurring: false,
    },
    {
      uuid: 'e10ce4e3-0e91-4b97-bc6c-9b5068e58428',
      appointmentNumber: '0000',
      patient: {
        identifier: '100GEJ',
        name: 'Neil Amstrong',
        uuid: '8673ee4f-e2ab-4077-ba55-4980f408773e',
        gender: 'M',
        age: 35,
        birthdate: '1986-04-03T00:00:00.000+0000',
        phoneNumber: '**********',
      },
      service: {
        appointmentServiceId: 1,
        name: 'Outpatient',
        description: null,
        speciality: {},
        startTime: '',
        endTime: '',
        maxAppointmentsLimit: null,
        durationMins: null,
        location: {},
        uuid: 'e2ec9cf0-ec38-4d2b-af6c-59c82fa30b90',
        initialAppointmentStatus: 'Scheduled',
        creatorName: null,
      },
      serviceType: {
        display: 'HIV Clinic',
        uuid: '53d58ff1-0c45-4e2e-9bd2-9cc826cb46e1',
        duration: 15,
      },
      provider: {
        uuid: 'f9badd80-ab76-11e2-9e96-0800200c9a66',
        person: { uuid: '24252571-dd5a-11e6-9d9c-0242ac150002', display: 'Dr James Cook' },
      },
      location: { name: 'HIV Clinic', uuid: '2131aff8-2e2a-480a-b7ab-4ac53250262b' },
      startDateTime: 1631278200000,
      endDateTime: 1631278560000,
      appointmentKind: 'WalkIn',
      status: 'Scheduled',
      comments: 'Some additional notes',
      additionalInfo: null,
      providers: [{ uuid: '24252571-dd5a-11e6-9d9c-0242ac150002', display: 'Dr James Cook' }],
      recurring: false,
    },
    {
      uuid: 'cdb0676f-0805-4c3e-bfef-7757a005e892',
      appointmentNumber: '0000',
      patient: {
        identifier: '100GEJ',
        name: 'Charles Babbage',
        uuid: '8673ee4f-e2ab-4077-ba55-4980f408773e',
        gender: 'M',
        age: 35,
        birthdate: '1986-04-03T00:00:00.000+0000',
        phoneNumber: '**********',
      },
      service: {
        appointmentServiceId: 1,
        name: 'Outpatient',
        description: null,
        speciality: {},
        startTime: '',
        endTime: '',
        maxAppointmentsLimit: null,
        durationMins: null,
        location: {},
        uuid: 'e2ec9cf0-ec38-4d2b-af6c-59c82fa30b90',
        initialAppointmentStatus: 'Scheduled',
        creatorName: null,
      },
      serviceType: {
        display: 'TB Clinic',
        uuid: '4a228e52-0bfe-11ed-861d-0242ac120002',
        duration: 15,
      },
      provider: null,
      location: { name: 'TB Clinic', uuid: '2131aff8-2e2a-480a-b7ab-4ac53250262b' },
      startDateTime: 1631537400000,
      endDateTime: 1631537760000,
      appointmentKind: 'WalkIn',
      status: 'Scheduled',
      comments: 'Some additional notes',
      additionalInfo: null,
      providers: [],
      recurring: false,
    },
    {
      uuid: '66565d8b-4849-4b7c-966a-554d6073f80c',
      appointmentNumber: '0000',
      patient: {
        identifier: '100GEJ',
        name: 'Elon Musketeer',
        uuid: '8673ee4f-e2ab-4077-ba55-4980f408773e',
        gender: 'M',
        age: 35,
        birthdate: '1986-04-03T00:00:00.000+0000',
        phoneNumber: '**********',
      },
      service: {
        appointmentServiceId: 1,
        name: 'Outpatient',
        description: null,
        speciality: {},
        startTime: '',
        endTime: '',
        maxAppointmentsLimit: null,
        durationMins: null,
        location: {},
        uuid: 'e2ec9cf0-ec38-4d2b-af6c-59c82fa30b90',
        initialAppointmentStatus: 'Scheduled',
        creatorName: null,
      },
      serviceType: {
        display: 'HIV Clinic',
        uuid: '53d58ff1-0c45-4e2e-9bd2-9cc826cb46e1',
        duration: 15,
      },
      provider: null,
      location: { name: 'HIV Clinic', uuid: '2131aff8-2e2a-480a-b7ab-4ac53250262b' },
      startDateTime: 1631605800000,
      endDateTime: 1631606100000,
      appointmentKind: 'WalkIn',
      status: 'Scheduled',
      comments: 'Some additional notes',
      additionalInfo: null,
      providers: [],
      recurring: false,
    },
    {
      uuid: '45dcc19d-dd14-4a07-95c6-afa264972a34',
      appointmentNumber: '0000',
      patient: {
        identifier: '100GEJ',
        name: 'Hopkins Derrick',
        uuid: '8673ee4f-e2ab-4077-ba55-4980f408773e',
        gender: 'M',
        age: 35,
        birthdate: '1986-04-03T00:00:00.000+0000',
        phoneNumber: '**********',
      },
      service: {
        appointmentServiceId: 1,
        name: 'Outpatient',
        description: null,
        speciality: {},
        startTime: '',
        endTime: '',
        maxAppointmentsLimit: null,
        durationMins: null,
        location: {},
        uuid: 'e2ec9cf0-ec38-4d2b-af6c-59c82fa30b90',
        initialAppointmentStatus: 'Scheduled',
        creatorName: null,
      },
      serviceType: {
        display: 'TB Clinic',
        uuid: '4a228e52-0bfe-11ed-861d-0242ac120002',
        duration: 15,
      },
      provider: {
        uuid: 'f9badd80-ab76-11e2-9e96-0800200c9a66',
        person: { uuid: '24252571-dd5a-11e6-9d9c-0242ac150002', display: 'Dr James Cook' },
      },
      location: { name: 'TB Clinic', uuid: '2131aff8-2e2a-480a-b7ab-4ac53250262b' },
      startDateTime: 1631623800000,
      endDateTime: 1631624160000,
      appointmentKind: 'WalkIn',
      status: 'Scheduled',
      comments: 'Some additional notes',
      additionalInfo: null,
      providers: [],
      recurring: false,
    },
    {
      uuid: 'fa4657ad-db46-487d-8e2e-a3858c906ae6',
      appointmentNumber: '0000',
      patient: {
        identifier: '100GEJ',
        name: 'Amos Strong',
        uuid: '8673ee4f-e2ab-4077-ba55-4980f408773e',
        gender: 'M',
        age: 35,
        birthdate: '1986-04-03T00:00:00.000+0000',
        phoneNumber: '**********',
      },
      service: {
        appointmentServiceId: 1,
        name: 'Outpatient',
        description: null,
        speciality: {},
        startTime: '',
        endTime: '',
        maxAppointmentsLimit: null,
        durationMins: null,
        location: {},
        uuid: 'e2ec9cf0-ec38-4d2b-af6c-59c82fa30b90',
        initialAppointmentStatus: 'Scheduled',
        creatorName: null,
      },
      serviceType: {
        display: 'TB Clinic',
        uuid: '4a228e52-0bfe-11ed-861d-0242ac120002',
        duration: 15,
      },
      provider: null,
      location: { name: 'TB Clinic', uuid: '2131aff8-2e2a-480a-b7ab-4ac53250262b' },
      startDateTime: 1631712720000,
      endDateTime: 1631713080000,
      appointmentKind: 'WalkIn',
      status: 'Scheduled',
      comments: 'Some value',
      additionalInfo: null,
      providers: [],
      recurring: false,
    },
  ],
};

export const mockPatient = {
  identifier: '100GEJ',
  name: 'John Wilson',
  uuid: '8673ee4f-e2ab-4077-ba55-4980f408773e',
  gender: 'M',
  age: 35,
  birthdate: '1986-04-03T00:00:00.000+0000',
  phoneNumber: '**********',
};

export const mockProviders = {
  data: [
    {
      uuid: 'f9badd80-ab76-11e2-9e96-0800200c9a66',
      person: { uuid: '24252571-dd5a-11e6-9d9c-0242ac150002', display: 'Dr James Cook' },
      display: 'doctor - James Cook',
    },
    {
      uuid: '3191eddf-5cc5-4fa4-94ef-dfc25e8d33e4',
      person: { uuid: '89af8ba6-2ec5-4d77-b3ed-7e9e02448e96', display: 'Dr Amstrong Neil' },
      display: 'doctor - Amstrong Neil',
    },
  ],
};

export const mockUseAppointmentServiceData = [
  {
    appointmentServiceId: 1,
    name: 'Outpatient',
    description: null,
    speciality: {},
    startDateTime: new Date().toISOString(),
    endTime: '',
    maxAppointmentsLimit: null,
    durationMins: 15,
    location: {},
    uuid: 'e2ec9cf0-ec38-4d2b-af6c-59c82fa30b90',
    color: '#006400',
    initialAppointmentStatus: 'Scheduled',
    creatorName: null,
    weeklyAvailability: [
      {
        dayOfWeek: 'MONDAY',
        startTime: '07:00:00',
        endTime: '20:00:00',
        maxAppointmentsLimit: null,
        uuid: '7c7c53c8-c104-40cc-9926-50fc6fe4c4c1',
      },
      {
        dayOfWeek: 'TUESDAY',
        startTime: '07:00:00',
        endTime: '20:00:00',
        maxAppointmentsLimit: null,
        uuid: '7683b94e-6c48-4132-b402-54837a8c0fb2',
      },
      {
        dayOfWeek: 'SUNDAY',
        startTime: '07:00:00',
        endTime: '20:00:00',
        maxAppointmentsLimit: null,
        uuid: '00be8427-0037-4984-8875-6a5a2bc57e8e',
      },
      {
        dayOfWeek: 'FRIDAY',
        startTime: '07:00:00',
        endTime: '20:00:00',
        maxAppointmentsLimit: null,
        uuid: 'af6b8d5b-be05-4e24-8601-30573f848bec',
      },
      {
        dayOfWeek: 'THURSDAY',
        startTime: '07:00:00',
        endTime: '20:00:00',
        maxAppointmentsLimit: null,
        uuid: 'eb35e91b-6909-41fe-9d09-750b83fb3b9c',
      },
      {
        dayOfWeek: 'SATURDAY',
        startTime: '07:00:00',
        endTime: '20:00:00',
        maxAppointmentsLimit: null,
        uuid: '7f6347fd-c514-4fd2-ab79-d7fd760bf82f',
      },
      {
        dayOfWeek: 'WEDNESDAY',
        startTime: '07:00:00',
        endTime: '20:00:00',
        maxAppointmentsLimit: null,
        uuid: 'dad83f54-a0a2-4ba9-819b-01e906c89b69',
      },
    ],
    serviceTypes: [{ duration: 15, name: 'Chemotherapy', uuid: '53d58ff1-0c45-4e2e-9bd2-9cc826cb46e1' }],
  },
];

export const mockMappedAppointmentsData = {
  data: [
    {
      id: 'e10ce4e3-0e91-4b97-bc6c-9b5068e58428',
      name: 'John Wilson',
      age: '45',
      gender: 'M',
      phoneNumber: '**********',
      dob: '1986-04-03T00:00:00.000+0000',
      patientUuid: '8673ee4f-e2ab-4077-ba55-4980f408773e',
      dateTime: '	30-Aug-2021, 03:35 PM',
      serviceType: 'HIV Clinic',
      visitType: 'HIV Clinic',
      provider: 'Dr James Cook',
      location: 'HIV Clinic',
      comments: 'Some additional notes',
      appointmentNumber: '0001',
      serviceUuid: 'ce03060e-1cbe-11ed-861d-0242ac120002',
      appointmentKind: 'Scheduled',
      status: 'Cancelled',
    },
    {
      id: 'cdb0676f-0805-4c3e-bfef-7757a005e892',
      name: 'Eric Test Ric',
      age: '32',
      gender: 'M',
      phoneNumber: '**********',
      dob: '1986-04-03T00:00:00.000+0000',
      patientUuid: '8673ee4f-e2ab-4077-ba55-4980f408773e',
      dateTime: '10-Sept-2021, 03:50 PM',
      serviceType: 'TB Clinic',
      visitType: 'TB Clinic',
      provider: 'Dr James Cook',
      location: 'TB Clinic',
      comments: 'Some additional notes here',
      appointmentNumber: '0001',
      serviceUuid: 'c674bc34-1cbe-11ed-861d-0242ac120002',
      appointmentKind: 'WalkIn',
      status: 'Scheduled',
    },
  ],
};
