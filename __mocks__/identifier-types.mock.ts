export const mockIdentifierTypes = [
  {
    name: 'OpenMRS ID',
    fieldName: 'OpenMRS ID',
    required: true,
    uuid: '05a29f94-c0ed-11e2-94be-8c13b969e334',
    format: null,
    isPrimary: true,
    uniquenessBehavior: 'UNIQUE' as const,
    identifierSources: [
      {
        uuid: '8549f706-7e85-4c1d-9424-217d50a2988b',
        name: 'Generator for Iqlinetest ID',
        description: 'Generator for Iqlinetest ID',
        baseCharacterSet: '0123456789ACDEFGHJKLMNPRTUVWXY',
        prefix: '',
        suffix: '',
        firstIdentifierBase: '100000',
        minLength: 7,
        maxLength: 7,
        identifierType: {
          uuid: '05a29f94-c0ed-11e2-94be-8c13b969e334',
          display: 'OpenMRS ID',
          name: 'OpenMRS ID',
          description: 'OpenMRS patient identifier, with check-digit',
          format: null,
          formatDescription: null,
          required: true,
          validator: 'org.openmrs.module.idgen.validator.LuhnMod30IdentifierValidator',
          locationBehavior: 'NOT_USED',
          uniquenessBehavior: 'UNIQUE',
          retired: false,
          links: [
            {
              rel: 'self',
              uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/05a29f94-c0ed-11e2-94be-8c13b969e334',
              resourceAlias: 'patientidentifiertype',
            },
            {
              rel: 'full',
              uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/05a29f94-c0ed-11e2-94be-8c13b969e334?v=full',
              resourceAlias: 'patientidentifiertype',
            },
          ],
          resourceVersion: '2.0',
        },
        links: [
          {
            rel: 'self',
            uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/idgen/identifiersource/8549f706-7e85-4c1d-9424-217d50a2988b',
            resourceAlias: 'identifiersource',
          },
        ],
        type: 'sequentialidentifiergenerator',
        resourceVersion: '1.8',
        autoGenerationOption: {
          uuid: '2be74f07-a4a0-4bfc-a943-8555d0074a74',
          identifierType: {
            uuid: '05a29f94-c0ed-11e2-94be-8c13b969e334',
            display: 'OpenMRS ID',
            name: 'OpenMRS ID',
            description: 'OpenMRS patient identifier, with check-digit',
            format: null,
            formatDescription: null,
            required: true,
            validator: 'org.openmrs.module.idgen.validator.LuhnMod30IdentifierValidator',
            locationBehavior: 'NOT_USED',
            uniquenessBehavior: 'UNIQUE',
            retired: false,
            links: [
              {
                rel: 'self',
                uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/05a29f94-c0ed-11e2-94be-8c13b969e334',
                resourceAlias: 'patientidentifiertype',
              },
              {
                rel: 'full',
                uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/05a29f94-c0ed-11e2-94be-8c13b969e334?v=full',
                resourceAlias: 'patientidentifiertype',
              },
            ],
            resourceVersion: '2.0',
          },
          location: null,
          source: {
            uuid: '8549f706-7e85-4c1d-9424-217d50a2988b',
            name: 'Generator for Iqlinetest ID',
            description: 'Generator for Iqlinetest ID',
            baseCharacterSet: '0123456789ACDEFGHJKLMNPRTUVWXY',
            prefix: '',
            suffix: '',
            firstIdentifierBase: '100000',
            minLength: 7,
            maxLength: 7,
            identifierType: {
              uuid: '05a29f94-c0ed-11e2-94be-8c13b969e334',
              display: 'OpenMRS ID',
              name: 'OpenMRS ID',
              description: 'OpenMRS patient identifier, with check-digit',
              format: null,
              formatDescription: null,
              required: true,
              validator: 'org.openmrs.module.idgen.validator.LuhnMod30IdentifierValidator',
              locationBehavior: 'NOT_USED',
              uniquenessBehavior: 'UNIQUE',
              retired: false,
              links: [
                {
                  rel: 'self',
                  uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/05a29f94-c0ed-11e2-94be-8c13b969e334',
                  resourceAlias: 'patientidentifiertype',
                },
                {
                  rel: 'full',
                  uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/05a29f94-c0ed-11e2-94be-8c13b969e334?v=full',
                  resourceAlias: 'patientidentifiertype',
                },
              ],
              resourceVersion: '2.0',
            },
            links: [
              {
                rel: 'self',
                uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/idgen/identifiersource/8549f706-7e85-4c1d-9424-217d50a2988b',
                resourceAlias: 'identifiersource',
              },
            ],
            type: 'sequentialidentifiergenerator',
            resourceVersion: '1.8',
          },
          manualEntryEnabled: false,
          automaticGenerationEnabled: true,
          links: [
            {
              rel: 'self',
              uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/idgen/autogenerationoption/2be74f07-a4a0-4bfc-a943-8555d0074a74',
              resourceAlias: 'autogenerationoption',
            },
          ],
          resourceVersion: '1.8',
        },
      },
      {
        uuid: '1cb84fc6-2330-4218-960f-add0d4f21bdd',
        name: 'New identifier',
        description: 'Description',
        baseCharacterSet: '0123456789ACDEFGHJKLMNPRTUVWXY',
        prefix: null,
        suffix: null,
        firstIdentifierBase: '50000',
        minLength: 3,
        maxLength: 10,
        identifierType: {
          uuid: '05a29f94-c0ed-11e2-94be-8c13b969e334',
          display: 'OpenMRS ID',
          name: 'OpenMRS ID',
          description: 'OpenMRS patient identifier, with check-digit',
          format: null,
          formatDescription: null,
          required: true,
          validator: 'org.openmrs.module.idgen.validator.LuhnMod30IdentifierValidator',
          locationBehavior: 'NOT_USED',
          uniquenessBehavior: 'UNIQUE',
          retired: false,
          links: [
            {
              rel: 'self',
              uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/05a29f94-c0ed-11e2-94be-8c13b969e334',
              resourceAlias: 'patientidentifiertype',
            },
            {
              rel: 'full',
              uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/05a29f94-c0ed-11e2-94be-8c13b969e334?v=full',
              resourceAlias: 'patientidentifiertype',
            },
          ],
          resourceVersion: '2.0',
        },
        links: [
          {
            rel: 'self',
            uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/idgen/identifiersource/1cb84fc6-2330-4218-960f-add0d4f21bdd',
            resourceAlias: 'identifiersource',
          },
        ],
        type: 'sequentialidentifiergenerator',
        resourceVersion: '1.8',
      },
      {
        uuid: '4a9f93a1-ef61-4c75-822d-97fe0e6ff744',
        name: 'New identifier',
        description: 'Description',
        baseCharacterSet: '0123456789ACDEFGHJKLMNPRTUVWXY',
        prefix: null,
        suffix: 'KU',
        firstIdentifierBase: '50000',
        minLength: 3,
        maxLength: 10,
        identifierType: {
          uuid: '05a29f94-c0ed-11e2-94be-8c13b969e334',
          display: 'OpenMRS ID',
          name: 'OpenMRS ID',
          description: 'OpenMRS patient identifier, with check-digit',
          format: null,
          formatDescription: null,
          required: true,
          validator: 'org.openmrs.module.idgen.validator.LuhnMod30IdentifierValidator',
          locationBehavior: 'NOT_USED',
          uniquenessBehavior: 'UNIQUE',
          retired: false,
          links: [
            {
              rel: 'self',
              uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/05a29f94-c0ed-11e2-94be-8c13b969e334',
              resourceAlias: 'patientidentifiertype',
            },
            {
              rel: 'full',
              uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/05a29f94-c0ed-11e2-94be-8c13b969e334?v=full',
              resourceAlias: 'patientidentifiertype',
            },
          ],
          resourceVersion: '2.0',
        },
        links: [
          {
            rel: 'self',
            uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/idgen/identifiersource/4a9f93a1-ef61-4c75-822d-97fe0e6ff744',
            resourceAlias: 'identifiersource',
          },
        ],
        type: 'sequentialidentifiergenerator',
        resourceVersion: '1.8',
      },
    ],
  },
  {
    name: 'ID Card',
    fieldName: 'idCard',
    required: false,
    uuid: 'b4143563-16cd-4439-b288-f83d61670fc8',
    format: null,
    isPrimary: false,
    uniquenessBehavior: 'UNIQUE' as const,
    identifierSources: [],
  },
  {
    name: 'Legacy ID',
    fieldName: 'legacyId',
    required: false,
    uuid: '*************-459e-a32e-d93b17eda533',
    format: null,
    isPrimary: false,
    uniquenessBehavior: null,
    identifierSources: [],
  },
  {
    name: 'Old Identification Number',
    fieldName: 'oldIdentificationNumber',
    required: false,
    uuid: '8d79403a-c2cc-11de-8d13-0010c6dffd0f',
    format: '',
    isPrimary: false,
    uniquenessBehavior: null,
    identifierSources: [
      {
        uuid: '46cf2aef-a9e4-46e8-a8fd-6eebb05fdc0f',
        name: 'Yet Another ID Generator',
        description: '',
        baseCharacterSet: '0123456789',
        prefix: '',
        suffix: '',
        firstIdentifierBase: '10000',
        minLength: 5,
        maxLength: 10,
        identifierType: {
          uuid: '8d79403a-c2cc-11de-8d13-0010c6dffd0f',
          display: 'Old Identification Number',
          name: 'Old Identification Number',
          description: 'Number given out prior to the OpenMRS system (No check digit)',
          format: '',
          formatDescription: null,
          required: false,
          validator: null,
          locationBehavior: null,
          uniquenessBehavior: null,
          retired: false,
          links: [
            {
              rel: 'self',
              uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/8d79403a-c2cc-11de-8d13-0010c6dffd0f',
              resourceAlias: 'patientidentifiertype',
            },
            {
              rel: 'full',
              uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/8d79403a-c2cc-11de-8d13-0010c6dffd0f?v=full',
              resourceAlias: 'patientidentifiertype',
            },
          ],
          resourceVersion: '2.0',
        },
        links: [
          {
            rel: 'self',
            uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/idgen/identifiersource/46cf2aef-a9e4-46e8-a8fd-6eebb05fdc0f',
            resourceAlias: 'identifiersource',
          },
        ],
        type: 'sequentialidentifiergenerator',
        resourceVersion: '1.8',
      },
    ],
  },
  {
    name: 'OpenMRS Identification Number',
    fieldName: 'openMrsIdentificationNumber',
    required: false,
    uuid: '8d793bee-c2cc-11de-8d13-0010c6dffd0f',
    format: '',
    isPrimary: false,
    uniquenessBehavior: null,
    identifierSources: [],
  },
  {
    name: 'SSN',
    fieldName: 'ssn',
    required: false,
    uuid: 'a71403f3-8584-4289-ab41-2b4e5570bd45',
    format: '[A-Z]{1}-[0-9]{7}',
    isPrimary: false,
    uniquenessBehavior: 'UNIQUE' as const,
    identifierSources: [],
  },
  {
    name: 'TestID',
    fieldName: 'testId',
    required: false,
    uuid: '80086c07-115d-46fc-9109-6538288d61eb',
    format: null,
    isPrimary: false,
    uniquenessBehavior: 'UNIQUE' as const,
    identifierSources: [],
  },
];

export const mockOpenmrsId = {
  openMrsId: {
    identifierTypeUuid: '05a29f94-c0ed-11e2-94be-8c13b969e334',
    identifierName: 'OpenMRS ID',
    preferred: true,
    initialValue: '',
    required: true,
    selectedSource: {
      uuid: '8549f706-7e85-4c1d-9424-217d50a2988b',
      name: 'Generator for Iqlinetest ID',
      description: 'Generator for Iqlinetest ID',
      baseCharacterSet: '0123456789ACDEFGHJKLMNPRTUVWXY',
      prefix: '',
      suffix: '',
      firstIdentifierBase: '100000',
      minLength: 7,
      maxLength: 7,
      identifierType: {
        uuid: '05a29f94-c0ed-11e2-94be-8c13b969e334',
        display: 'OpenMRS ID',
        name: 'OpenMRS ID',
        description: 'OpenMRS patient identifier, with check-digit',
        format: null,
        formatDescription: null,
        required: true,
        validator: 'org.openmrs.module.idgen.validator.LuhnMod30IdentifierValidator',
        locationBehavior: 'NOT_USED',
        uniquenessBehavior: 'UNIQUE',
        retired: false,
        links: [
          {
            rel: 'self',
            uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/05a29f94-c0ed-11e2-94be-8c13b969e334',
            resourceAlias: 'patientidentifiertype',
          },
          {
            rel: 'full',
            uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/05a29f94-c0ed-11e2-94be-8c13b969e334?v=full',
            resourceAlias: 'patientidentifiertype',
          },
        ],
        resourceVersion: '2.0',
      },
      links: [
        {
          rel: 'self',
          uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/idgen/identifiersource/8549f706-7e85-4c1d-9424-217d50a2988b',
          resourceAlias: 'identifiersource',
        },
      ],
      type: 'sequentialidentifiergenerator',
      resourceVersion: '1.8',
      autoGenerationOption: {
        uuid: '2be74f07-a4a0-4bfc-a943-8555d0074a74',
        identifierType: {
          uuid: '05a29f94-c0ed-11e2-94be-8c13b969e334',
          display: 'OpenMRS ID',
          name: 'OpenMRS ID',
          description: 'OpenMRS patient identifier, with check-digit',
          format: null,
          formatDescription: null,
          required: true,
          validator: 'org.openmrs.module.idgen.validator.LuhnMod30IdentifierValidator',
          locationBehavior: 'NOT_USED',
          uniquenessBehavior: 'UNIQUE',
          retired: false,
          links: [
            {
              rel: 'self',
              uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/05a29f94-c0ed-11e2-94be-8c13b969e334',
              resourceAlias: 'patientidentifiertype',
            },
            {
              rel: 'full',
              uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/05a29f94-c0ed-11e2-94be-8c13b969e334?v=full',
              resourceAlias: 'patientidentifiertype',
            },
          ],
          resourceVersion: '2.0',
        },
        location: null,
        source: {
          uuid: '8549f706-7e85-4c1d-9424-217d50a2988b',
          name: 'Generator for Iqlinetest ID',
          description: 'Generator for Iqlinetest ID',
          baseCharacterSet: '0123456789ACDEFGHJKLMNPRTUVWXY',
          prefix: '',
          suffix: '',
          firstIdentifierBase: '100000',
          minLength: 7,
          maxLength: 7,
          identifierType: {
            uuid: '05a29f94-c0ed-11e2-94be-8c13b969e334',
            display: 'OpenMRS ID',
            name: 'OpenMRS ID',
            description: 'OpenMRS patient identifier, with check-digit',
            format: null,
            formatDescription: null,
            required: true,
            validator: 'org.openmrs.module.idgen.validator.LuhnMod30IdentifierValidator',
            locationBehavior: 'NOT_USED',
            uniquenessBehavior: 'UNIQUE',
            retired: false,
            links: [
              {
                rel: 'self',
                uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/05a29f94-c0ed-11e2-94be-8c13b969e334',
                resourceAlias: 'patientidentifiertype',
              },
              {
                rel: 'full',
                uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/patientidentifiertype/05a29f94-c0ed-11e2-94be-8c13b969e334?v=full',
                resourceAlias: 'patientidentifiertype',
              },
            ],
            resourceVersion: '2.0',
          },
          links: [
            {
              rel: 'self',
              uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/idgen/identifiersource/8549f706-7e85-4c1d-9424-217d50a2988b',
              resourceAlias: 'identifiersource',
            },
          ],
          type: 'sequentialidentifiergenerator',
          resourceVersion: '1.8',
        },
        manualEntryEnabled: false,
        automaticGenerationEnabled: true,
        links: [
          {
            rel: 'self',
            uri: 'http://dev3.openmrs.org/openmrs/ws/rest/v1/idgen/autogenerationoption/2be74f07-a4a0-4bfc-a943-8555d0074a74',
            resourceAlias: 'autogenerationoption',
          },
        ],
        resourceVersion: '1.8',
      },
    },
    autoGeneration: true,
    identifierValue: 'auto-generated',
  },
};
