export * from './active-visits.mock';
export * from './address.mock';
export * from './appointments.mock';
export * from './auto-generation-options.mock';
export * from './emr-configuration.mock';
export * from './identifier-types.mock';
export * from './identifiers.mock';
export * from './locations.mock';
export * from './metrics.mock';
export * from './patient.mock';
export * from './patient-appointments.mock';
export * from './patient-registration.mock';
export * from './queue-entry.mock';
export * from './queue-rooms.mock';
export * from './search.mock';
export * from './session.mock';
export * from './wards.mock';
export * from './inpatient-request';
export * from './visits.mock';
